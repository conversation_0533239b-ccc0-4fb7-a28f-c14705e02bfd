declare global {
  interface Window {
    paypal?: {
      Buttons: (options: PayPalButtonsOptions) => {
        render: (selector: string) => Promise<void>
      }
    }
  }
}

interface PayPalButtonsOptions {
  style?: {
    layout?: 'vertical' | 'horizontal'
    color?: 'gold' | 'blue' | 'silver' | 'white' | 'black'
    shape?: 'rect' | 'pill'
    label?: 'paypal' | 'checkout' | 'buynow' | 'pay' | 'installment' | 'subscribe'
    height?: number
    tagline?: boolean
  }
  createSubscription?: (data: any, actions: PayPalActions) => Promise<string>
  onApprove?: (data: PayPalApprovalData, actions: PayPalActions) => Promise<void>
  onError?: (err: any) => void
  onCancel?: (data: any) => void
}

interface PayPalActions {
  subscription: {
    create: (subscriptionData: PayPalSubscriptionData) => Promise<string>
  }
}

interface PayPalApprovalData {
  subscriptionID: string
  orderID?: string
}

interface PayPalSubscriptionData {
  plan_id: string
  application_context?: {
    brand_name?: string
    locale?: string
    shipping_preference?: 'NO_SHIPPING' | 'SET_PROVIDED_ADDRESS' | 'GET_FROM_FILE'
    user_action?: 'SUBSCRIBE_NOW' | 'CONTINUE'
    payment_method?: {
      payer_selected?: 'PAYPAL'
      payee_preferred?: 'IMMEDIATE_PAYMENT_REQUIRED' | 'UNRESTRICTED'
    }
    return_url?: string
    cancel_url?: string
  }
}

export {}
