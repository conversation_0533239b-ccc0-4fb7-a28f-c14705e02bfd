'use client'

import { use<PERSON><PERSON><PERSON>, useRouter, useSearchParams } from 'next/navigation'
import { useEffect, useState, Suspense } from 'react'
import { useAuthState } from 'react-firebase-hooks/auth'
import { auth } from '@/lib/firebase'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Check, ArrowLeft, Loader2 } from 'lucide-react'
import { toast } from '@/hooks/use-toast'
import Script from 'next/script'

// Plan configurations
const PLAN_CONFIGS = {
  pro: {
    name: 'Pro Plan',
    price: 6.69,
    description: 'Perfect for growing HR teams',
    features: [
      '50 jobs per month',
      '100 resumes per job',
      'AI Screening',
      'CSV Export',
      'Resume Database',
      'Priority support',
      'Delete Resume after 180 days',
      '1 user only',
    ],
    badge: 'POPULAR',
    badgeColor: 'bg-[#1aa8e0]',
  },
  plus: {
    name: 'Plus Plan',
    price: 12.99,
    description: 'Perfect for bulk hiring',
    features: [
      '100 jobs per month',
      '100 resumes per job',
      'AI Screening',
      'CSV Export',
      'Resume Database',
      'Priority support',
      'Delete Resume after 180 days',
      '1 user only',
    ],
    badge: 'BEST VALUE',
    badgeColor: 'bg-[#8529db]',
  },
}

function CheckoutPageContent() {
  const params = useParams()
  const router = useRouter()
  const searchParams = useSearchParams()
  const [user, loading, error] = useAuthState(auth)
  const [isPayPalLoaded, setIsPayPalLoaded] = useState(false)
  const [isProcessing, setIsProcessing] = useState(false)

  const tier = params.tier as string
  const planConfig = PLAN_CONFIGS[tier as keyof typeof PLAN_CONFIGS]

  // Get plan ID based on tier
  const getPlanId = () => {
    if (tier === 'pro') return 'P-229517095U197632VNBJTBXA'
    if (tier === 'plus') return 'P-6V244598UL0600708NBJTBXA'
    return ''
  }

  // Redirect if invalid tier
  useEffect(() => {
    if (!loading && (!tier || !planConfig)) {
      router.push('/pricing')
    }
  }, [tier, planConfig, loading, router])

  // Redirect if not authenticated
  useEffect(() => {
    if (!loading && !user) {
      router.push('/signin?redirect=' + encodeURIComponent(`/checkout/${tier}`))
    }
  }, [user, loading, tier, router])

  // Initialize PayPal buttons when SDK is loaded
  useEffect(() => {
    if (isPayPalLoaded && user && window.paypal) {
      initializePayPalButtons()
    }
  }, [isPayPalLoaded, user])

  const initializePayPalButtons = () => {
    if (!window.paypal || !user) return

    const planId = getPlanId()
    if (!planId) return

    // Clear any existing buttons
    const container = document.getElementById('paypal-button-container')
    if (container) {
      container.innerHTML = ''
    }

    window.paypal.Buttons({
      style: {
        layout: 'vertical',
        color: 'blue',
        shape: 'rect',
        label: 'subscribe',
        height: 50,
      },
      createSubscription: function(data: any, actions: any) {
        setIsProcessing(true)
        return actions.subscription.create({
          'plan_id': planId,
          'application_context': {
            'brand_name': 'The Consult Now',
            'locale': 'en-US',
            'shipping_preference': 'NO_SHIPPING',
            'user_action': 'SUBSCRIBE_NOW',
            'payment_method': {
              'payer_selected': 'PAYPAL',
              'payee_preferred': 'IMMEDIATE_PAYMENT_REQUIRED',
            },
            'return_url': `${window.location.origin}/checkout/success?tier=${tier}`,
            'cancel_url': `${window.location.origin}/checkout/cancelled`,
          }
        })
      },
      onApprove: async function(data: any, actions: any) {
        try {
          // Activate subscription on our backend
          const response = await fetch('/api/paypal/activate-subscription', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${await user.getIdToken()}`,
            },
            body: JSON.stringify({
              subscriptionId: data.subscriptionID,
            }),
          })

          const result = await response.json()

          if (!response.ok) {
            throw new Error(result.error || 'Failed to activate subscription')
          }

          toast({
            title: 'Subscription Activated!',
            description: `Your ${tier} plan is now active.`,
          })

          // Redirect to success page
          router.push(`/checkout/success?tier=${tier}&subscription_id=${data.subscriptionID}`)

        } catch (error) {
          console.error('Error activating subscription:', error)
          toast({
            title: 'Error',
            description: error instanceof Error ? error.message : 'Failed to activate subscription',
            variant: 'destructive',
          })
        } finally {
          setIsProcessing(false)
        }
      },
      onError: function(err: any) {
        console.error('PayPal error:', err)
        toast({
          title: 'Payment Error',
          description: 'There was an error processing your payment. Please try again.',
          variant: 'destructive',
        })
        setIsProcessing(false)
      },
      onCancel: function(data: any) {
        console.log('PayPal payment cancelled:', data)
        toast({
          title: 'Payment Cancelled',
          description: 'Your payment was cancelled. You can try again anytime.',
        })
        setIsProcessing(false)
      }
    }).render('#paypal-button-container')
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-black via-gray-900 to-black flex items-center justify-center">
        <Loader2 className="w-8 h-8 animate-spin text-[#1aa8e0]" />
      </div>
    )
  }

  if (!planConfig) {
    return null
  }

  return (
    <>
      {/* PayPal SDK Script */}
      <Script
        src={`https://www.paypal.com/sdk/js?client-id=${process.env.NEXT_PUBLIC_PAYPAL_CLIENT_ID}&vault=true&intent=subscription`}
        onLoad={() => setIsPayPalLoaded(true)}
        strategy="lazyOnload"
      />

      <div className="min-h-screen bg-white">

        <div className="container mx-auto px-4 py-12">
          {/* Back Button */}
          <Button
            variant="ghost"
            onClick={() => router.push('/pricing')}
            className="mb-8 text-gray-600 hover:text-gray-800"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Pricing
          </Button>

          <div className="max-w-6xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
              {/* Left Side - Plan Name */}
              <div className="flex items-center justify-center">
                <div className="text-center">
                  <h1 className="text-4xl font-bold text-gray-900 mb-4">{planConfig.name} Plan</h1>
                  <p className="text-xl text-gray-600">{planConfig.description}</p>
                </div>
              </div>

              {/* Right Side - Payment Details */}
              <div className="bg-white border-2 border-gray-200 rounded-lg p-8">
                {/* Price */}
                <div className="mb-6">
                  <span className="text-4xl font-bold text-gray-900">${planConfig.price}</span>
                  <span className="text-gray-600 text-lg">/month</span>
                </div>

                {/* PayPal Payment Section */}
                <div className="space-y-4">

                  {/* PayPal Buttons Container */}
                  {isPayPalLoaded && user ? (
                    <div className="relative">
                      {isProcessing && (
                        <div className="absolute inset-0 bg-gray-100/80 backdrop-blur-sm rounded-lg flex items-center justify-center z-10">
                          <div className="text-center">
                            <Loader2 className="w-8 h-8 animate-spin text-[#1aa8e0] mx-auto mb-2" />
                            <p className="text-gray-900 text-sm">Processing your subscription...</p>
                          </div>
                        </div>
                      )}
                      <div id="paypal-button-container" className="min-h-[120px]"></div>
                    </div>
                  ) : (
                    <div className="flex items-center justify-center py-8">
                      <Loader2 className="w-6 h-6 animate-spin text-[#1aa8e0] mr-2" />
                      <span className="text-gray-600">Loading payment options...</span>
                    </div>
                  )}

                  {/* Security Note */}
                  <div className="text-center space-y-2 mt-6">
                    <p className="text-sm text-gray-600">
                      🔒 Secure payment powered by PayPal. Cancel anytime.
                    </p>
                    <p className="text-sm text-gray-600">
                      💳 Supports PayPal, Visa, Mastercard, American Express, and more
                    </p>
                    <p className="text-sm text-gray-600 mt-4">
                      By subscribing, you agree to our{' '}
                      <a href="/terms" className="text-[#1aa8e0] hover:underline">
                        Terms of Service
                      </a>{' '}
                      and{' '}
                      <a href="/privacy" className="text-[#1aa8e0] hover:underline">
                        Privacy Policy
                      </a>
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}

export default function CheckoutPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gradient-to-br from-black via-gray-900 to-black flex items-center justify-center">
        <Loader2 className="w-8 h-8 animate-spin text-[#1aa8e0]" />
      </div>
    }>
      <CheckoutPageContent />
    </Suspense>
  )
}
