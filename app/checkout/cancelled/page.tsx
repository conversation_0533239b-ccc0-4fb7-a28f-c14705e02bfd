'use client'

import { useRouter } from 'next/navigation'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { XCircle, ArrowLeft } from 'lucide-react'

export default function CheckoutCancelledPage() {
  const router = useRouter()

  return (
    <div className="min-h-screen bg-gradient-to-br from-black via-gray-900 to-black">
      {/* Background Effects */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-[#8529db] rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-[#1aa8e0] rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-2000"></div>
      </div>

      <div className="relative z-10 container mx-auto px-4 py-12">
        <div className="max-w-2xl mx-auto">
          <Card className="bg-black/40 backdrop-blur-xl border-gray-700/30">
            <CardHeader className="text-center">
              <XCircle className="w-16 h-16 mx-auto text-orange-500 mb-4" />
              <CardTitle className="text-2xl text-white">Subscription Cancelled</CardTitle>
              <CardDescription className="text-gray-400">
                Your subscription process was cancelled. No charges were made.
              </CardDescription>
            </CardHeader>

            <CardContent className="text-center space-y-6">
              <div className="bg-orange-500/10 border border-orange-500/20 rounded-lg p-4">
                <p className="text-orange-400 text-sm">
                  Don't worry! You can still use our free plan with 3 jobs per month and 50 resumes per job.
                </p>
              </div>

              <div className="space-y-3">
                <Button
                  onClick={() => router.push('/pricing')}
                  className="w-full bg-[#1aa8e0] text-white hover:bg-[#1aa8e0]/90"
                >
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back to Pricing
                </Button>
                
                <Button
                  onClick={() => router.push('/start-analysing')}
                  variant="outline"
                  className="w-full border-gray-600 text-gray-300 hover:text-white hover:border-[#1aa8e0]"
                >
                  Continue with Free Plan
                </Button>
              </div>

              {/* Why Subscribe Section */}
              <div className="mt-8 text-left">
                <h3 className="text-lg font-semibold text-white mb-4">Why upgrade?</h3>
                <ul className="space-y-2 text-gray-300 text-sm">
                  <li className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-[#1aa8e0] rounded-full"></div>
                    Process more jobs and resumes per month
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-[#1aa8e0] rounded-full"></div>
                    Priority customer support
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-[#1aa8e0] rounded-full"></div>
                    Extended data retention (180 days)
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-[#1aa8e0] rounded-full"></div>
                    Advanced analytics and insights
                  </li>
                </ul>
              </div>
            </CardContent>
          </Card>

          {/* Support Info */}
          <div className="text-center mt-8">
            <p className="text-gray-400 text-sm">
              Have questions? Contact us at{' '}
              <a href="mailto:<EMAIL>" className="text-[#1aa8e0] hover:underline">
                <EMAIL>
              </a>
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
