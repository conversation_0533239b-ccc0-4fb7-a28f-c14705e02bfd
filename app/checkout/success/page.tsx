'use client'

import { useEffect, useState, Suspense } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { useAuthState } from 'react-firebase-hooks/auth'
import { auth } from '@/lib/firebase'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { CheckCircle, Loader2, AlertCircle } from 'lucide-react'
import { toast } from '@/hooks/use-toast'

function CheckoutSuccessContent() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [user, loading] = useAuthState(auth)
  const [isActivating, setIsActivating] = useState(false)
  const [activationStatus, setActivationStatus] = useState<'pending' | 'success' | 'error'>('pending')
  const [errorMessage, setErrorMessage] = useState('')

  const subscriptionId = searchParams.get('subscription_id')
  const tier = searchParams.get('tier')

  useEffect(() => {
    if (!loading && !user) {
      router.push('/signin')
      return
    }

    if (user && subscriptionId) {
      activateSubscription()
    }
  }, [user, loading, subscriptionId])

  const activateSubscription = async () => {
    if (!user || !subscriptionId) return

    setIsActivating(true)

    try {
      const response = await fetch('/api/paypal/activate-subscription', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${await user.getIdToken()}`,
        },
        body: JSON.stringify({
          subscriptionId: subscriptionId,
        }),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to activate subscription')
      }

      setActivationStatus('success')
      toast({
        title: 'Subscription Activated!',
        description: `Your ${data.tier} plan is now active.`,
      })

    } catch (error) {
      console.error('Error activating subscription:', error)
      setActivationStatus('error')
      setErrorMessage(error instanceof Error ? error.message : 'Failed to activate subscription')
      toast({
        title: 'Activation Error',
        description: error instanceof Error ? error.message : 'Failed to activate subscription',
        variant: 'destructive',
      })
    } finally {
      setIsActivating(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-black via-gray-900 to-black flex items-center justify-center">
        <Loader2 className="w-8 h-8 animate-spin text-[#1aa8e0]" />
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-black via-gray-900 to-black">
      {/* Background Effects */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-[#8529db] rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-[#1aa8e0] rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-2000"></div>
      </div>

      <div className="relative z-10 container mx-auto px-4 py-12">
        <div className="max-w-2xl mx-auto">
          <Card className="bg-black/40 backdrop-blur-xl border-gray-700/30">
            <CardHeader className="text-center">
              {activationStatus === 'pending' && (
                <>
                  <Loader2 className="w-16 h-16 mx-auto text-[#1aa8e0] animate-spin mb-4" />
                  <CardTitle className="text-2xl text-white">Activating Your Subscription</CardTitle>
                  <CardDescription className="text-gray-400">
                    Please wait while we activate your {tier} plan...
                  </CardDescription>
                </>
              )}

              {activationStatus === 'success' && (
                <>
                  <CheckCircle className="w-16 h-16 mx-auto text-green-500 mb-4" />
                  <CardTitle className="text-2xl text-white">Subscription Activated!</CardTitle>
                  <CardDescription className="text-gray-400">
                    Your {tier} plan is now active and ready to use.
                  </CardDescription>
                </>
              )}

              {activationStatus === 'error' && (
                <>
                  <AlertCircle className="w-16 h-16 mx-auto text-red-500 mb-4" />
                  <CardTitle className="text-2xl text-white">Activation Failed</CardTitle>
                  <CardDescription className="text-gray-400">
                    {errorMessage || 'There was an error activating your subscription.'}
                  </CardDescription>
                </>
              )}
            </CardHeader>

            <CardContent className="text-center space-y-4">
              {activationStatus === 'success' && (
                <>
                  <div className="bg-green-500/10 border border-green-500/20 rounded-lg p-4 mb-6">
                    <p className="text-green-400 text-sm">
                      🎉 Welcome to {tier} plan! You can now access all premium features.
                    </p>
                  </div>

                  <div className="space-y-3">
                    <Button
                      onClick={() => router.push('/start-analysing')}
                      className="w-full bg-[#1aa8e0] text-white hover:bg-[#1aa8e0]/90"
                    >
                      Start Analyzing Resumes
                    </Button>
                    
                    <Button
                      onClick={() => router.push('/profile')}
                      variant="outline"
                      className="w-full border-gray-600 text-gray-300 hover:text-white hover:border-[#1aa8e0]"
                    >
                      View Subscription Details
                    </Button>
                  </div>
                </>
              )}

              {activationStatus === 'error' && (
                <div className="space-y-3">
                  <Button
                    onClick={activateSubscription}
                    disabled={isActivating}
                    className="w-full bg-[#1aa8e0] text-white hover:bg-[#1aa8e0]/90"
                  >
                    {isActivating ? (
                      <>
                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        Retrying...
                      </>
                    ) : (
                      'Retry Activation'
                    )}
                  </Button>
                  
                  <Button
                    onClick={() => router.push('/pricing')}
                    variant="outline"
                    className="w-full border-gray-600 text-gray-300 hover:text-white hover:border-[#1aa8e0]"
                  >
                    Back to Pricing
                  </Button>
                </div>
              )}

              {activationStatus === 'pending' && (
                <p className="text-gray-500 text-sm">
                  This may take a few moments. Please don't close this page.
                </p>
              )}
            </CardContent>
          </Card>

          {/* Support Info */}
          <div className="text-center mt-8">
            <p className="text-gray-400 text-sm">
              Need help? Contact our support team at{' '}
              <a href="mailto:<EMAIL>" className="text-[#1aa8e0] hover:underline">
                <EMAIL>
              </a>
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default function CheckoutSuccessPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gradient-to-br from-black via-gray-900 to-black flex items-center justify-center">
        <Loader2 className="w-8 h-8 animate-spin text-[#1aa8e0]" />
      </div>
    }>
      <CheckoutSuccessContent />
    </Suspense>
  )
}
