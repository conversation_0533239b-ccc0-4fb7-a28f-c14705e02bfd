import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/firebase';
import { verifyAuthToken } from '@/lib/auth-middleware';
import { doc, updateDoc, getDoc } from 'firebase/firestore';
import { GoogleGenAI } from '@google/genai';
import mammoth from 'mammoth';

// Gemini API configuration
const GEMINI_API_KEY = process.env.GEMINI_API_KEY || 'AIzaSyAH-u76OZZIWOanyqu6iaPLKZM0Tk8kE0Y';
const GEMINI_MODEL = process.env.GEMINI_MODEL || 'gemini-2.5-flash-preview-05-20';

// Initialize the Gemini client with the new Google GenAI SDK
const ai = new GoogleGenAI({
  apiKey: GEMINI_API_KEY,
});

// Configuration for API calls
const generationConfig = {
  temperature: 0.55,
  responseMimeType: 'application/json'
};

console.log('Gemini API configured for complete resume processing with model:', GEMINI_MODEL);

// Helper function to call Gemini API with file for complete processing (extraction + analysis)
async function callGeminiAPIForCompleteProcessing(
  promptText: string,
  base64Data: string,
  mimeType: string,
  systemInstruction?: string
): Promise<string> {
  // Combine system instruction with prompt if provided
  const fullPrompt = systemInstruction ? `${systemInstruction}\n\n${promptText}` : promptText;

  const contents = [
    { text: fullPrompt },
    {
      inlineData: {
        mimeType: mimeType,
        data: base64Data
      }
    }
  ];

  const response = await ai.models.generateContent({
    model: GEMINI_MODEL,
    contents: contents,
    config: generationConfig
  });

  return response.text || '';
}

// Helper function to get MIME type from file extension
function getMimeTypeFromExtension(fileName: string): string {
  const extension = fileName.toLowerCase().split('.').pop();

  switch (extension) {
    case 'pdf':
      return 'application/pdf';
    case 'doc':
      return 'application/msword';
    case 'docx':
      return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
    case 'txt':
      return 'text/plain';
    default:
      return 'application/pdf'; // Default fallback
  }
}

// Helper function to merge work history from extraction and analysis
function mergeWorkHistory(extractedWorkHistory: any[], analysisWorkHistory: any[]) {
  if (!extractedWorkHistory || extractedWorkHistory.length === 0) {
    return analysisWorkHistory || [];
  }

  if (!analysisWorkHistory || analysisWorkHistory.length === 0) {
    return extractedWorkHistory;
  }

  // Merge by matching company and position
  return extractedWorkHistory.map((extractedWork, index) => {
    const analysisWork = analysisWorkHistory[index] || {};

    return {
      company: extractedWork.company || analysisWork.company || "Company not specified",
      position: extractedWork.position || analysisWork.position || "Position not specified",
      duration: extractedWork.duration || analysisWork.duration || "Duration not specified",
      description: extractedWork.description || "Description not available", // Preserve from extraction
      relevance: analysisWork.relevance || "medium" // Add from analysis
    };
  });
}

// Helper function to update resume status in Firestore with improved error handling
async function updateResumeStatus(
  jobId: string,
  resumeId: string,
  status: string,
  data?: any
): Promise<boolean> {
  const MAX_RETRIES = 5;
  const RETRY_DELAY_MS = 2000;

  for (let attempt = 0; attempt < MAX_RETRIES; attempt++) {
    try {
      console.log(`Updating resume ${resumeId} status to ${status} (attempt ${attempt + 1}/${MAX_RETRIES})`);

      const jobRef = doc(db, 'jobs', jobId);
      const jobDoc = await getDoc(jobRef);

      if (!jobDoc.exists()) {
        console.error(`Job ${jobId} not found`);
        return false;
      }

      const jobData = jobDoc.data();
      const resumes = jobData.resumes || [];

      // Find resume by ID with multiple matching strategies
      let resumeIndex = resumes.findIndex((r: any) => r.id === resumeId);

      // If not found by ID, try matching by URL
      if (resumeIndex === -1) {
        resumeIndex = resumes.findIndex((r: any) => r.url === resumeId);
      }

      // If still not found, try partial URL matching
      if (resumeIndex === -1) {
        resumeIndex = resumes.findIndex((r: any) =>
          r.url && resumeId && (r.url.includes(resumeId) || resumeId.includes(r.url))
        );
      }

      if (resumeIndex === -1) {
        console.error(`Resume ${resumeId} not found in job ${jobId}. Available resume IDs:`,
          resumes.map((r: any) => ({ id: r.id, url: r.url })));
        return false;
      }

      // Update resume data with comprehensive status tracking
      const updatedResume = {
        ...resumes[resumeIndex],
        extractionStatus: status,
        analysisStatus: status,
        analyzed: status === 'completed',
        lastUpdated: new Date().toISOString(),
        processingAttempts: (resumes[resumeIndex].processingAttempts || 0) + 1,
        ...data
      };

      // If this is an error status, preserve the error information
      if (status === 'failed' || status === 'error') {
        updatedResume.errorDetails = {
          timestamp: new Date().toISOString(),
          attempt: attempt + 1,
          ...(data?.error && { error: data.error })
        };
      }

      resumes[resumeIndex] = updatedResume;

      await updateDoc(jobRef, { resumes });
      console.log(`Successfully updated resume ${resumeId} with status: ${status}`);
      return true;

    } catch (error) {
      console.error(`Attempt ${attempt + 1} failed to update resume ${resumeId}:`, error);
      if (attempt < MAX_RETRIES - 1) {
        const delay = RETRY_DELAY_MS * Math.pow(2, attempt); // Exponential backoff
        console.log(`Waiting ${delay}ms before retry...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }

  console.error(`Failed to update resume ${resumeId} after ${MAX_RETRIES} attempts`);
  return false;
}

// Main function to process a single resume completely (extraction + analysis)
async function processResumeComplete(
  resumeUrl: string,
  resumeName: string,
  resumeId: string,
  jobId: string,
  jobDetails: {title: string, location: string, description: string, skills: string, minExperience?: number, maxExperience?: number}
) {
  console.log(`Starting complete processing for resume: ${resumeName} (ID: ${resumeId})`);

  try {
    // Update status to processing with detailed logging
    console.log(`Step 1: Updating status to 'processing' for resume ${resumeId}`);
    const statusUpdateSuccess = await updateResumeStatus(jobId, resumeId, 'processing');
    if (!statusUpdateSuccess) {
      console.warn(`Failed to update status to processing, but continuing with analysis`);
    }

    // Download resume file with retry logic
    console.log(`Step 2: Downloading resume file from Firebase Storage: ${resumeUrl}`);

    let response;
    let downloadAttempts = 0;
    const MAX_DOWNLOAD_ATTEMPTS = 3;

    while (downloadAttempts < MAX_DOWNLOAD_ATTEMPTS) {
      try {
        response = await fetch(resumeUrl);
        if (response.ok) {
          break;
        } else {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
      } catch (downloadError) {
        downloadAttempts++;
        console.error(`Download attempt ${downloadAttempts} failed:`, downloadError);

        if (downloadAttempts >= MAX_DOWNLOAD_ATTEMPTS) {
          throw new Error(`Failed to download file after ${MAX_DOWNLOAD_ATTEMPTS} attempts: ${downloadError}`);
        }

        // Wait before retrying with exponential backoff
        const delay = 1000 * Math.pow(2, downloadAttempts);
        console.log(`Waiting ${delay}ms before retry...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    if (!response) {
      throw new Error('Failed to download file: No response received');
    }

    // Get the file data as a buffer
    console.log(`Step 3: Processing downloaded file`);
    let arrayBuffer = await response.arrayBuffer();
    let fileBuffer = new Uint8Array(arrayBuffer);

    const contentType = response.headers.get('content-type') || '';

    // Handle DOCX text extraction before sending to Gemini
    let base64Data: string;
    let mimeType: string;
    const originalMimeType = getMimeTypeFromExtension(resumeName);
    const isDocxFile = originalMimeType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';

    if (isDocxFile) {
      console.log('DOCX file detected, extracting text content...');
      try {
        const result = await mammoth.extractRawText({buffer: Buffer.from(arrayBuffer)});
        const textContent = result.value;

        if (!textContent || textContent.trim().length === 0) {
          throw new Error('No text content extracted from DOCX file');
        }

        base64Data = Buffer.from(textContent, 'utf-8').toString('base64');
        mimeType = 'text/plain';
        console.log(`DOCX text extraction successful. Text length: ${textContent.length} characters`);
        console.log('Extracted text preview (first 200 chars):', textContent.substring(0, 200));
      } catch (extractionError) {
        console.error('DOCX text extraction failed:', extractionError);
        throw new Error(`Failed to extract text from DOCX file: ${extractionError instanceof Error ? extractionError.message : 'Unknown error'}`);
      }
    } else {
      // Existing logic for other file types (PDF, TXT, etc.)
      base64Data = Buffer.from(arrayBuffer).toString('base64');
      mimeType = originalMimeType;
    }

    console.log(`File processed successfully. Size: ${arrayBuffer.byteLength} bytes, Original MIME: ${originalMimeType}, Final MIME: ${mimeType}, Was DOCX: ${isDocxFile}`);

    // Create comprehensive prompt for both extraction and analysis
    const promptText = `
    You are an expert resume analyzer. Please perform BOTH extraction and analysis of this resume against the job requirements.

    JOB DETAILS:
    - Title: ${jobDetails.title}
    - Location: ${jobDetails.location}
    - Description: ${jobDetails.description}
    - Required Skills: ${jobDetails.skills}
    - Experience Requirements: ${jobDetails.minExperience !== undefined && jobDetails.maxExperience !== undefined
      ? `${jobDetails.minExperience}-${jobDetails.maxExperience} years`
      : jobDetails.minExperience !== undefined
        ? `${jobDetails.minExperience}+ years minimum`
        : jobDetails.maxExperience !== undefined
          ? `Up to ${jobDetails.maxExperience} years`
          : 'Not specified'}

    Please provide a comprehensive response with the following structure:

    {
      "extractedData": {
        "name": "Full name of the candidate",
        "email": "Email address",
        "phone": "Phone number",
        "location": "Current location/address",
        "summary": "Professional summary or objective",
        "skills": ["skill1", "skill2", "skill3", ...],
        "workHistory": [
          {
            "company": "Company name",
            "position": "Job title",
            "duration": "Start date - End date",
            "description": "Detailed job description, responsibilities, and key achievements",
            "years": 2.5
          }
        ],
        "education": [
          {
            "institution": "School/University name",
            "degree": "Degree type and field",
            "year": "Graduation year",
            "gpa": "GPA if available"
          }
        ],
        "certifications": ["Certification 1", "Certification 2", ...],
        "totalExperience": 5.5,
        "relevantExperience": 3.2
      },
      "analysis": {
        "matchScore": 85,
        "matchQuality": "top",
        "matchedSkills": ["skill1", "skill2", ...],
        "missingSkills": ["skill3", "skill4", ...],
        "scoreBreakdown": {
          "skillsMatchScore": 42.5,
          "experienceScore": 18.0,
          "educationScore": 12.0,
          "jobTitleScore": 9.0,
          "locationScore": 4.5
        },
        "experienceLevel": {
          "totalYears": 5.5,
          "relevantYears": 3.2
        },
        "workHistory": [
          {
            "company": "Company name",
            "position": "Position",
            "duration": "Duration",
            "description": "Brief description of responsibilities and achievements",
            "relevance": "high/medium/low"
          }
        ],
        "strengths": "Key strengths relative to the job",
        "weaknesses": "Areas for improvement",
        "recommendations": "Hiring recommendations",
        "finalVerdict": "2-line final assessment of the candidate"
      }
    }

    SCORING CRITERIA:
    - Skills Match (50 points): How well candidate's skills align with job requirements
    - Experience Level (20 points): Years of experience and seniority level - consider both total years and relevant years against the specified experience requirements
    - Education (15 points): Educational background relevance
    - Job Title Relevance (10 points): How relevant their previous roles are to this position
    - Location (5 points): Geographic compatibility

    MATCH QUALITY CLASSIFICATION:
    - top: 80-100 match score
    - medium: 60-79 match score
    - low: 0-59 match score

    Extract all available information and provide detailed analysis. If any field is not available, use null or appropriate default values.
    `;

    // Call Gemini API for complete processing with retry logic
    console.log(`Step 4: Calling Gemini API for complete resume processing (extraction + analysis)`);

    let responseText;
    let geminiAttempts = 0;
    const MAX_GEMINI_ATTEMPTS = 3;

    while (geminiAttempts < MAX_GEMINI_ATTEMPTS) {
      try {
        responseText = await callGeminiAPIForCompleteProcessing(
          promptText,
          base64Data,
          mimeType,
          "You are an expert resume analyzer. Extract structured data and provide comprehensive analysis in the exact JSON format specified."
        );

        if (responseText && responseText.trim().length > 0) {
          console.log('Gemini API response received successfully');
          console.log('Response text from Gemini (first 200 chars):', responseText.substring(0, 200));
          break;
        } else {
          throw new Error('Empty response from Gemini API');
        }
      } catch (geminiError) {
        geminiAttempts++;
        console.error(`Gemini API attempt ${geminiAttempts} failed:`, geminiError);

        if (geminiAttempts >= MAX_GEMINI_ATTEMPTS) {
          throw new Error(`Failed to get response from Gemini API after ${MAX_GEMINI_ATTEMPTS} attempts: ${geminiError}`);
        }

        // Wait before retrying
        const delay = 2000 * geminiAttempts;
        console.log(`Waiting ${delay}ms before Gemini retry...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    // Parse the response with improved error handling
    console.log(`Step 5: Parsing Gemini API response`);
    let parsedResponse;

    if (!responseText) {
      throw new Error('No response text received from Gemini API');
    }

    try {
      // Try to parse the entire response as JSON
      parsedResponse = JSON.parse(responseText);
      console.log('Successfully parsed response as complete JSON');
    } catch (initialParseError) {
      console.log('Could not parse entire response as JSON, trying to extract JSON object');

      // Try to extract JSON object using regex pattern
      const jsonMatch = responseText.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        console.log('JSON object match found in response');
        try {
          parsedResponse = JSON.parse(jsonMatch[0]);
          console.log('Successfully parsed extracted JSON object');
        } catch (jsonParseError) {
          console.error('Error parsing extracted JSON object:', jsonParseError);
          console.error('Problematic JSON string:', jsonMatch[0].substring(0, 500));
          const errorMessage = jsonParseError instanceof Error ? jsonParseError.message : 'Unknown parsing error';
          throw new Error(`Failed to parse JSON response from Gemini: ${errorMessage}`);
        }
      } else {
        console.error('No JSON object found in response');
        console.error('Response content:', responseText.substring(0, 1000));
        throw new Error('Failed to extract JSON object from Gemini response');
      }
    }

    // Validate response structure with detailed logging
    console.log('Response structure validation:');
    console.log('- Has extractedData:', !!parsedResponse.extractedData);
    console.log('- Has analysis:', !!parsedResponse.analysis);

    if (!parsedResponse.extractedData || !parsedResponse.analysis) {
      console.error('Invalid response structure. Available keys:', Object.keys(parsedResponse));
      throw new Error('Invalid response structure: missing extractedData or analysis');
    }

    const extractedData = parsedResponse.extractedData;
    const analysis = parsedResponse.analysis;

    console.log('Extracted data keys:', Object.keys(extractedData));
    console.log('Analysis data keys:', Object.keys(analysis));

    // Prepare data for Firestore update with comprehensive validation
    console.log(`Step 6: Preparing data for Firestore update`);

    const resumeDataToSave = {
      // Core extracted data - store in the structure the dashboard expects
      extractedData: extractedData,

      // Map basic info to root level for dashboard compatibility
      basicInfo: {
        fullName: extractedData.name || extractedData.basicInfo?.fullName || resumeName,
        email: extractedData.email || extractedData.basicInfo?.email || '',
        phone: extractedData.phone || extractedData.basicInfo?.phone || '',
        location: extractedData.location || extractedData.basicInfo?.location || ''
      },

      // Map skills to root level
      skills: extractedData.skills || [],

      // Map education to root level
      education: extractedData.education || [],

      // Map work history to root level
      workHistory: mergeWorkHistory(extractedData.workHistory || [], analysis.workHistory || []),

      // Map experience level to root level
      experienceLevel: {
        totalYears: extractedData.totalExperience || analysis.experienceLevel?.totalYears || 0,
        relevantYears: extractedData.relevantExperience || analysis.experienceLevel?.relevantYears || 0
      },

      // Analysis results with fallbacks
      matchScore: typeof analysis.matchScore === 'number' ? analysis.matchScore : 0,
      matchQuality: analysis.matchQuality || 'low',
      matchedSkills: Array.isArray(analysis.matchedSkills) ? analysis.matchedSkills : [],
      missingSkills: Array.isArray(analysis.missingSkills) ? analysis.missingSkills : [],
      scoreBreakdown: analysis.scoreBreakdown || {
        skillsMatchScore: 0,
        experienceScore: 0,
        educationScore: 0,
        jobTitleScore: 0,
        locationScore: 0
      },

      // Additional analysis data
      strengths: analysis.strengths || '',
      weaknesses: analysis.weaknesses || '',
      recommendations: analysis.recommendations || '',
      finalVerdict: analysis.finalVerdict || 'Analysis completed successfully',

      // Status tracking
      analyzed: true,
      extractionStatus: 'completed',
      analysisStatus: 'completed',
      lastUpdated: new Date().toISOString(),
      processingCompletedAt: new Date().toISOString(),

      // Store candidate name from extracted data
      name: extractedData.name || extractedData.basicInfo?.fullName || resumeName
    };

    console.log('Resume data prepared for saving:', {
      matchScore: resumeDataToSave.matchScore,
      matchQuality: resumeDataToSave.matchQuality,
      matchedSkillsCount: resumeDataToSave.matchedSkills.length,
      missingSkillsCount: resumeDataToSave.missingSkills.length,
      candidateName: resumeDataToSave.name,
      hasBasicInfo: !!resumeDataToSave.basicInfo,
      hasExtractedData: !!resumeDataToSave.extractedData,
      hasWorkHistory: !!resumeDataToSave.workHistory && resumeDataToSave.workHistory.length > 0,
      hasEducation: !!resumeDataToSave.education && resumeDataToSave.education.length > 0,
      hasSkills: !!resumeDataToSave.skills && resumeDataToSave.skills.length > 0
    });

    // Update resume in Firestore with detailed logging
    console.log(`Step 7: Updating resume data in Firestore`);
    const updateSuccess = await updateResumeStatus(jobId, resumeId, 'completed', resumeDataToSave);

    if (updateSuccess) {
      console.log(`✅ Successfully completed processing for resume: ${resumeName} (ID: ${resumeId})`);
      return {
        success: true,
        resumeId: resumeId,
        resumeName: resumeName,
        data: {
          extractedData: extractedData,
          analysis: analysis
        }
      };
    } else {
      throw new Error('Failed to update resume data in Firestore');
    }

  } catch (error) {
    console.error(`❌ Error processing resume ${resumeName} (ID: ${resumeId}):`, error);

    // Prepare error details for storage
    const errorDetails = {
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
      stack: error instanceof Error ? error.stack : undefined
    };

    // Update status to failed with error details
    await updateResumeStatus(jobId, resumeId, 'failed', { errorDetails });

    return {
      success: false,
      resumeId: resumeId,
      resumeName: resumeName,
      error: error instanceof Error ? error.message : 'Unknown error',
      errorDetails: errorDetails
    };
  }
}

export async function POST(request: NextRequest) {
  try {
    console.log('API route called: /api/process-resume-complete');

    // Parse the request body
    const body = await request.json();
    const { resumeUrl, resumeName, resumeId, jobId, jobDetails, userId } = body;

    // Validate required fields
    if (!resumeUrl || !resumeName || !resumeId || !jobId || !jobDetails) {
      console.error('Missing required fields');
      return NextResponse.json(
        { error: 'Missing required fields: resumeUrl, resumeName, resumeId, jobId, jobDetails' },
        { status: 400 }
      );
    }

    // Validate job details structure with detailed logging
    console.log('Received jobDetails:', JSON.stringify(jobDetails, null, 2));

    if (!jobDetails.title || !jobDetails.description) {
      console.error('Invalid jobDetails structure:', {
        hasTitle: !!jobDetails.title,
        hasDescription: !!jobDetails.description,
        title: jobDetails.title,
        description: jobDetails.description?.substring(0, 100) + '...',
        allKeys: Object.keys(jobDetails)
      });
      return NextResponse.json(
        { error: 'Invalid jobDetails: title and description are required' },
        { status: 400 }
      );
    }

    // Verify authentication
    let verifiedUserId;
    try {
      const authResult = await verifyAuthToken(request);
      if (authResult.error) {
        console.warn('Authentication failed, using fallback user ID');
        verifiedUserId = userId || 'anonymous-user';
      } else {
        verifiedUserId = authResult.userId;
        console.log('User authenticated successfully:', verifiedUserId);
      }
    } catch (authError) {
      console.error('Error during auth verification:', authError);
      verifiedUserId = userId || 'anonymous-user';
      console.log('Using fallback user ID due to auth error:', verifiedUserId);
    }

    // Check if Gemini API key is configured
    if (!GEMINI_API_KEY) {
      console.error('Gemini API key is not configured');
      return NextResponse.json(
        { error: 'Gemini API key is not configured' },
        { status: 500 }
      );
    }

    try {
      // Process the resume completely
      const result = await processResumeComplete(
        resumeUrl,
        resumeName,
        resumeId,
        jobId,
        jobDetails
      );

      // Return the result
      return NextResponse.json({
        success: result.success,
        message: result.success
          ? `Successfully processed resume: ${resumeName}`
          : `Failed to process resume: ${resumeName}`,
        result: result
      });

    } catch (processingError) {
      console.error('Error during resume processing:', processingError);
      return NextResponse.json(
        {
          error: 'Failed to process resume',
          message: processingError instanceof Error ? processingError.message : 'Unknown processing error'
        },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Error in process-resume-complete API route:', error);

    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
