import { NextRequest, NextResponse } from 'next/server';
import { verifyAuthToken } from '@/lib/auth-middleware';
import { getSubscription, mapPlanToTier } from '@/lib/paypal';
import { db } from '@/lib/firebase';
import { doc, getDoc, updateDoc } from 'firebase/firestore';

export async function POST(request: NextRequest) {
  try {
    console.log('API route called: /api/paypal/activate-subscription');

    // Verify authentication
    const authResult = await verifyAuthToken(request);
    if (authResult.error) {
      return authResult.error;
    }
    const userId = authResult.userId;

    // Parse request body
    const body = await request.json();
    const { subscriptionId } = body;

    if (!subscriptionId) {
      return NextResponse.json(
        { error: 'Subscription ID is required' },
        { status: 400 }
      );
    }

    // Get user data
    const userRef = doc(db, 'users', userId);
    const userDoc = await getDoc(userRef);
    
    if (!userDoc.exists()) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    const userData = userDoc.data();

    // Verify this subscription belongs to the user
    if (userData.pendingPaypalSubscriptionId !== subscriptionId) {
      return NextResponse.json(
        { error: 'Invalid subscription ID for this user' },
        { status: 400 }
      );
    }

    try {
      // Get subscription details from PayPal
      const subscription = await getSubscription(subscriptionId);
      
      // Check if subscription is active
      if (subscription.status !== 'ACTIVE') {
        return NextResponse.json(
          { error: `Subscription is not active. Status: ${subscription.status}` },
          { status: 400 }
        );
      }

      // Determine tier from plan ID
      const tier = mapPlanToTier(subscription.planId || '');
      
      if (tier === 'Free') {
        return NextResponse.json(
          { error: 'Invalid subscription plan' },
          { status: 400 }
        );
      }

      // Update user document with active subscription
      const updateData = {
        tier: tier,
        paypalSubscriptionId: subscriptionId,
        paypalPlanId: subscription.planId,
        paypalSubscriptionStatus: subscription.status,
        subscriptionActivatedAt: new Date(),
        lastRenewalDate: new Date(),
        nextBillingDate: subscription.billingInfo?.nextBillingTime ? 
          new Date(subscription.billingInfo.nextBillingTime) : null,
        lastUpdated: new Date(),
        // Clear pending fields
        pendingPaypalSubscriptionId: null,
        pendingTier: null,
        // Reset usage for new billing cycle
        jobDescriptionCount: 0,
      };

      await updateDoc(userRef, updateData);

      console.log(`✅ Subscription activated for user ${userId}: ${tier} tier`);

      return NextResponse.json({
        success: true,
        message: 'Subscription activated successfully',
        tier: tier,
        subscriptionId: subscriptionId,
        status: subscription.status,
        nextBillingDate: updateData.nextBillingDate,
      });

    } catch (paypalError) {
      console.error('PayPal API error:', paypalError);
      return NextResponse.json(
        { 
          error: 'Failed to verify PayPal subscription',
          details: paypalError instanceof Error ? paypalError.message : 'Unknown PayPal error'
        },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Error in activate-subscription API:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
