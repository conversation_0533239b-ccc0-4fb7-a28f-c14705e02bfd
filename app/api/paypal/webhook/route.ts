import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/firebase';
import { collection, query, where, getDocs, updateDoc, doc, getDoc } from 'firebase/firestore';
import { verifyWebhookSignature, mapPlanToTier, type PayPalWebhookEvent } from '@/lib/paypal';

export async function POST(request: NextRequest) {
  try {
    console.log('PayPal webhook received');

    // Get headers and body
    const headers = Object.fromEntries(request.headers.entries());
    const body = await request.text();
    
    // Parse webhook event
    let event: PayPalWebhookEvent;
    try {
      event = JSON.parse(body);
    } catch (parseError) {
      console.error('Failed to parse webhook body:', parseError);
      return NextResponse.json({ error: 'Invalid JSON' }, { status: 400 });
    }

    console.log('Webhook event type:', event.event_type);
    console.log('Subscription ID:', event.resource.id);

    // Verify webhook signature (simplified for now)
    const isValid = verifyWebhookSignature(headers, body, 'webhook-id');
    if (!isValid) {
      console.error('Invalid webhook signature');
      return NextResponse.json({ error: 'Invalid signature' }, { status: 401 });
    }

    // Find user by subscription ID
    const subscriptionId = event.resource.id;
    const usersRef = collection(db, 'users');
    const q = query(usersRef, where('paypalSubscriptionId', '==', subscriptionId));
    const querySnapshot = await getDocs(q);

    if (querySnapshot.empty) {
      console.log('No user found for subscription ID:', subscriptionId);
      return NextResponse.json({ message: 'User not found' }, { status: 200 });
    }

    const userDoc = querySnapshot.docs[0];
    const userId = userDoc.id;
    const userData = userDoc.data();

    console.log('Processing webhook for user:', userId);

    // Handle different webhook events
    switch (event.event_type) {
      case 'BILLING.SUBSCRIPTION.ACTIVATED':
        await handleSubscriptionActivated(userId, event);
        break;
        
      case 'BILLING.SUBSCRIPTION.CANCELLED':
        await handleSubscriptionCancelled(userId, event);
        break;
        
      case 'BILLING.SUBSCRIPTION.SUSPENDED':
        await handleSubscriptionSuspended(userId, event);
        break;
        
      case 'BILLING.SUBSCRIPTION.PAYMENT.FAILED':
        await handlePaymentFailed(userId, event);
        break;
        
      case 'PAYMENT.SALE.COMPLETED':
        await handlePaymentCompleted(userId, event);
        break;
        
      default:
        console.log('Unhandled webhook event type:', event.event_type);
    }

    return NextResponse.json({ message: 'Webhook processed successfully' });

  } catch (error) {
    console.error('Error processing PayPal webhook:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

async function handleSubscriptionActivated(userId: string, event: PayPalWebhookEvent) {
  console.log('Handling subscription activated for user:', userId);
  
  const tier = mapPlanToTier(event.resource.plan_id || '');
  
  const updateData = {
    tier: tier,
    paypalSubscriptionStatus: 'ACTIVE',
    subscriptionActivatedAt: new Date(),
    lastRenewalDate: new Date(),
    lastUpdated: new Date(),
    jobDescriptionCount: 0, // Reset usage
  };

  await updateDoc(doc(db, 'users', userId), updateData);
  console.log('✅ Subscription activated for user:', userId);
}

async function handleSubscriptionCancelled(userId: string, event: PayPalWebhookEvent) {
  console.log('Handling subscription cancelled for user:', userId);
  
  const updateData = {
    tier: 'Free',
    paypalSubscriptionStatus: 'CANCELLED',
    subscriptionCancelledAt: new Date(),
    lastUpdated: new Date(),
    jobDescriptionCount: 0, // Reset to free tier limits
  };

  await updateDoc(doc(db, 'users', userId), updateData);
  console.log('✅ Subscription cancelled for user:', userId);
}

async function handleSubscriptionSuspended(userId: string, event: PayPalWebhookEvent) {
  console.log('Handling subscription suspended for user:', userId);
  
  const updateData = {
    tier: 'Free', // Downgrade to free tier
    paypalSubscriptionStatus: 'SUSPENDED',
    subscriptionSuspendedAt: new Date(),
    lastUpdated: new Date(),
    jobDescriptionCount: 0, // Reset to free tier limits
  };

  await updateDoc(doc(db, 'users', userId), updateData);
  console.log('✅ Subscription suspended for user:', userId);
}

async function handlePaymentFailed(userId: string, event: PayPalWebhookEvent) {
  console.log('Handling payment failed for user:', userId);

  // Extract transaction details from the failed payment event
  const transactionData = {
    id: event.resource.id || event.id,
    type: 'payment_failed',
    status: 'FAILED',
    amount: event.resource.amount || null,
    currency: event.resource.amount?.currency_code || 'USD',
    transactionId: event.resource.id || event.id,
    subscriptionId: event.resource.billing_agreement_id || event.resource.subscription_id,
    paypalTransactionId: event.resource.id,
    createdAt: new Date(),
    eventType: event.event_type,
    failureReason: event.resource.reason_code || 'Payment failed',
    rawEventData: event.resource, // Store full PayPal data for reference
  };

  // Get current user document to append transaction
  const userRef = doc(db, 'users', userId);
  const userDoc = await getDoc(userRef);

  if (!userDoc.exists()) {
    console.error('User document not found:', userId);
    return;
  }

  const userData = userDoc.data();
  const existingTransactions = userData.transactionHistory || [];

  // Add new failed transaction to the beginning of the array (most recent first)
  const updatedTransactions = [transactionData, ...existingTransactions];

  // Keep only the last 50 transactions to avoid document size issues
  const limitedTransactions = updatedTransactions.slice(0, 50);

  // Immediately downgrade to Free tier as per user preference
  const updateData = {
    tier: 'Free',
    paypalSubscriptionStatus: 'PAYMENT_FAILED',
    lastPaymentFailedAt: new Date(),
    lastUpdated: new Date(),
    jobDescriptionCount: 0, // Reset to free tier limits
    transactionHistory: limitedTransactions,
  };

  await updateDoc(userRef, updateData);
  console.log('✅ Payment failed - downgraded user to Free tier and transaction stored:', userId);
}

async function handlePaymentCompleted(userId: string, event: PayPalWebhookEvent) {
  console.log('Handling payment completed for user:', userId);

  // Extract transaction details from the event
  const transactionData = {
    id: event.resource.id || event.id,
    type: 'payment_completed',
    status: 'COMPLETED',
    amount: event.resource.amount || null,
    currency: event.resource.amount?.currency_code || 'USD',
    transactionId: event.resource.id || event.id,
    subscriptionId: event.resource.billing_agreement_id || event.resource.subscription_id,
    paypalTransactionId: event.resource.id,
    createdAt: new Date(),
    eventType: event.event_type,
    rawEventData: event.resource, // Store full PayPal data for reference
  };

  // Get current user document to append transaction
  const userRef = doc(db, 'users', userId);
  const userDoc = await getDoc(userRef);

  if (!userDoc.exists()) {
    console.error('User document not found:', userId);
    return;
  }

  const userData = userDoc.data();
  const existingTransactions = userData.transactionHistory || [];

  // Add new transaction to the beginning of the array (most recent first)
  const updatedTransactions = [transactionData, ...existingTransactions];

  // Keep only the last 50 transactions to avoid document size issues
  const limitedTransactions = updatedTransactions.slice(0, 50);

  const updateData = {
    lastRenewalDate: new Date(),
    lastUpdated: new Date(),
    jobDescriptionCount: 0, // Reset usage for new billing cycle
    paypalSubscriptionStatus: 'ACTIVE',
    transactionHistory: limitedTransactions,
  };

  await updateDoc(userRef, updateData);
  console.log('✅ Payment completed and transaction stored for user:', userId);
}
