import { NextRequest, NextResponse } from 'next/server';
import { verifyAuthToken } from '@/lib/auth-middleware';
import { createSubscription, getTierPlanId, PLAN_CONFIGS } from '@/lib/paypal';
import { db } from '@/lib/firebase';
import { doc, getDoc, updateDoc } from 'firebase/firestore';

export async function POST(request: NextRequest) {
  try {
    console.log('API route called: /api/paypal/create-subscription');

    // Verify authentication
    const authResult = await verifyAuthToken(request);
    if (authResult.error) {
      return authResult.error;
    }
    const userId = authResult.userId;

    // Parse request body
    const body = await request.json();
    const { tier } = body;

    // Validate tier
    if (!tier || !['Pro', 'Plus'].includes(tier)) {
      return NextResponse.json(
        { error: 'Invalid tier. Must be Pro or Plus.' },
        { status: 400 }
      );
    }

    // Get user data
    const userRef = doc(db, 'users', userId);
    const userDoc = await getDoc(userRef);
    
    if (!userDoc.exists()) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    const userData = userDoc.data();

    // Check if user already has an active subscription
    if (userData.paypalSubscriptionId && userData.tier !== 'Free') {
      return NextResponse.json(
        { error: 'User already has an active subscription' },
        { status: 400 }
      );
    }

    // Get plan ID for the tier
    const planId = getTierPlanId(tier as 'Pro' | 'Plus');
    
    // Create return and cancel URLs
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
    const returnUrl = `${baseUrl}/checkout/success?tier=${tier}`;
    const cancelUrl = `${baseUrl}/checkout/cancelled`;

    try {
      // Create PayPal subscription
      const subscription = await createSubscription(planId, returnUrl, cancelUrl);
      
      // Store pending subscription info in user document
      await updateDoc(userRef, {
        pendingPaypalSubscriptionId: subscription.id,
        pendingTier: tier,
        subscriptionCreatedAt: new Date(),
        lastUpdated: new Date(),
      });

      // Find approval URL from subscription links
      const approvalUrl = subscription.links?.find(
        (link: any) => link.rel === 'approve'
      )?.href;

      if (!approvalUrl) {
        throw new Error('No approval URL found in PayPal response');
      }

      return NextResponse.json({
        success: true,
        subscriptionId: subscription.id,
        approvalUrl: approvalUrl,
        planConfig: PLAN_CONFIGS[tier as 'Pro' | 'Plus'],
      });

    } catch (paypalError) {
      console.error('PayPal API error:', paypalError);
      return NextResponse.json(
        { 
          error: 'Failed to create PayPal subscription',
          details: paypalError instanceof Error ? paypalError.message : 'Unknown PayPal error'
        },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Error in create-subscription API:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
