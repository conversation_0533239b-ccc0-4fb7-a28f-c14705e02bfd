import { NextRequest, NextResponse } from 'next/server';
import { verifyAuthToken } from '@/lib/auth-middleware';
import { cancelSubscription } from '@/lib/paypal';
import { db } from '@/lib/firebase';
import { doc, getDoc, updateDoc } from 'firebase/firestore';

export async function POST(request: NextRequest) {
  try {
    console.log('API route called: /api/paypal/cancel-subscription');

    // Verify authentication
    const authResult = await verifyAuthToken(request);
    if (authResult.error) {
      return authResult.error;
    }
    const userId = authResult.userId;

    // Parse request body
    const body = await request.json();
    const { subscriptionId, reason } = body;

    if (!subscriptionId) {
      return NextResponse.json(
        { error: 'Subscription ID is required' },
        { status: 400 }
      );
    }

    // Get user data
    const userRef = doc(db, 'users', userId);
    const userDoc = await getDoc(userRef);
    
    if (!userDoc.exists()) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    const userData = userDoc.data();

    // Verify this subscription belongs to the user
    if (userData.paypalSubscriptionId !== subscriptionId) {
      return NextResponse.json(
        { error: 'Invalid subscription ID for this user' },
        { status: 400 }
      );
    }

    try {
      // Cancel subscription with PayPal
      await cancelSubscription(subscriptionId, reason || 'User requested cancellation');
      
      // Update user document
      const updateData = {
        paypalSubscriptionStatus: 'CANCELLED',
        subscriptionCancelledAt: new Date(),
        lastUpdated: new Date(),
      };

      await updateDoc(userRef, updateData);

      console.log(`✅ Subscription cancelled for user ${userId}: ${subscriptionId}`);

      return NextResponse.json({
        success: true,
        message: 'Subscription cancelled successfully',
        subscriptionId: subscriptionId,
      });

    } catch (paypalError) {
      console.error('PayPal API error:', paypalError);
      return NextResponse.json(
        { 
          error: 'Failed to cancel PayPal subscription',
          details: paypalError instanceof Error ? paypalError.message : 'Unknown PayPal error'
        },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Error in cancel-subscription API:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
