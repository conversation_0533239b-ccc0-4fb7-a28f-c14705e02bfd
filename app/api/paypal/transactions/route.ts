import { NextRequest, NextResponse } from 'next/server';
import { verifyAuthToken } from '@/lib/auth-middleware';
import { db } from '@/lib/firebase';
import { doc, getDoc } from 'firebase/firestore';

export async function GET(request: NextRequest) {
  try {
    console.log('API route called: /api/paypal/transactions');

    // Verify authentication
    const authResult = await verifyAuthToken(request);
    if (authResult.error) {
      return authResult.error;
    }
    const userId = authResult.userId;

    // Get user data from Firestore
    const userRef = doc(db, 'users', userId);
    const userDoc = await getDoc(userRef);
    
    if (!userDoc.exists()) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    const userData = userDoc.data();
    const transactionHistory = userData.transactionHistory || [];

    // Sort transactions by date (most recent first) and limit to 5 for display
    const sortedTransactions = transactionHistory
      .sort((a: any, b: any) => {
        const dateA = a.createdAt?.toDate ? a.createdAt.toDate() : new Date(a.createdAt);
        const dateB = b.createdAt?.toDate ? b.createdAt.toDate() : new Date(b.createdAt);
        return dateB.getTime() - dateA.getTime();
      })
      .slice(0, 5); // Show only recent 5 transactions

    // Format transactions for frontend
    const formattedTransactions = sortedTransactions.map((transaction: any) => {
      const createdAt = transaction.createdAt?.toDate ? 
        transaction.createdAt.toDate() : 
        new Date(transaction.createdAt);

      return {
        id: transaction.id,
        type: transaction.type,
        status: transaction.status,
        amount: transaction.amount,
        currency: transaction.currency || 'USD',
        transactionId: transaction.transactionId,
        paypalTransactionId: transaction.paypalTransactionId,
        subscriptionId: transaction.subscriptionId,
        createdAt: createdAt.toISOString(),
        eventType: transaction.eventType,
        failureReason: transaction.failureReason || null,
        // Generate PayPal receipt URL if it's a successful payment
        receiptUrl: transaction.status === 'COMPLETED' && transaction.paypalTransactionId ? 
          `https://www.paypal.com/activity/payment/${transaction.paypalTransactionId}` : null
      };
    });

    return NextResponse.json({
      success: true,
      transactions: formattedTransactions,
      totalCount: transactionHistory.length,
      hasMore: transactionHistory.length > 5
    });

  } catch (error) {
    console.error('Error in transactions API:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
