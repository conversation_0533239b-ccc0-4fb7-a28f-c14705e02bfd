import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/firebase';
import { verifyAuthToken } from '@/lib/auth-middleware';
import { doc, updateDoc, getDoc } from 'firebase/firestore';
import { GoogleGenAI } from '@google/genai';
import mammoth from 'mammoth';

// Helper function to determine the correct MIME type based on file extension
function getContentType(fileName: string): string {
  const extension = fileName.split('.').pop()?.toLowerCase();

  switch (extension) {
    case 'pdf':
      return 'application/pdf';
    case 'doc':
      return 'application/msword';
    case 'docx':
      return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
    case 'txt':
      return 'text/plain';
    default:
      return 'application/octet-stream'; // Default binary stream
  }
}

// Helper function to check if file type is supported by Gemini API for batch processing
function isFileTypeSupportedByGemini(fileName: string): boolean {
  const extension = fileName.toLowerCase().split('.').pop();
  // Gemini API supports PDF and TXT files reliably for batch processing
  // DOCX files are now supported via text extraction
  return extension === 'pdf' || extension === 'txt' || extension === 'docx';
}

// Gemini API configuration
const GEMINI_API_KEY = process.env.GEMINI_API_KEY || 'AIzaSyAH-u76OZZIWOanyqu6iaPLKZM0Tk8kE0Y';
const GEMINI_MODEL = process.env.GEMINI_MODEL || 'gemini-2.5-flash-preview-05-20';

// Initialize the Gemini client with the new Google GenAI SDK
const ai = new GoogleGenAI({
  apiKey: GEMINI_API_KEY,
});

// Configuration for API calls
const generationConfig = {
  temperature: 0.55,
  responseMimeType: 'application/json',
};

console.log('Gemini API configured for combined batch processing with model:', GEMINI_MODEL);

// Helper function to call Gemini API with multiple files for combined processing
async function callGeminiAPIWithMultipleFiles(promptText: string, files: Array<{base64Data: string, mimeType: string, fileName: string}>, systemInstruction?: string): Promise<string> {
  // Combine system instruction with prompt if provided
  const fullPrompt = systemInstruction ? `${systemInstruction}\n\n${promptText}` : promptText;

  const contents: any[] = [
    { text: fullPrompt }
  ];

  // Add each file to the contents array
  files.forEach((file) => {
    contents.push({
      inlineData: {
        mimeType: file.mimeType,
        data: file.base64Data
      }
    });
  });

  const response = await ai.models.generateContent({
    model: GEMINI_MODEL,
    contents: contents,
    config: generationConfig
  });

  return response.text || '';
}

// Helper function to update resume with combined data (extraction + analysis)
async function updateResumeWithCombinedData(jobId: string, resumeId: string, combinedData: any, resumeName: string, _resumeUrl: string): Promise<boolean> {
  const MAX_RETRIES = 3;
  const RETRY_DELAY_MS = 1000;

  for (let retryCount = 0; retryCount < MAX_RETRIES; retryCount++) {
    try {
      const jobRef = doc(db, 'jobs', jobId);
      const jobDoc = await getDoc(jobRef);

      if (!jobDoc.exists()) {
        console.error(`Job ${jobId} not found`);
        return false;
      }

      const jobData = jobDoc.data();
      const resumes = jobData.resumes || [];

      // Find and update the specific resume
      const resumeIndex = resumes.findIndex((r: any) => r.id === resumeId);
      if (resumeIndex === -1) {
        console.error(`Resume ${resumeId} not found in job ${jobId}`);
        return false;
      }

      // Prepare the combined resume data - use consistent structure with individual processing
      const extractedData = combinedData.extractedData || combinedData;
      const analysis = combinedData.analysis || combinedData;

      const resumeDataToSave = {
        // Core extracted data
        extractedData: extractedData,

        // Map basic info to root level for dashboard compatibility
        basicInfo: {
          fullName: extractedData.basicInfo?.fullName || extractedData.name || resumeName.split('.')[0],
          email: extractedData.basicInfo?.email || extractedData.email || '',
          phone: extractedData.basicInfo?.phone || extractedData.phone || '',
          location: extractedData.basicInfo?.location || extractedData.location || ''
        },

        // Map skills to root level
        skills: extractedData.skills || [],

        // Map education to root level
        education: extractedData.education || [],

        // Map work history to root level
        workHistory: extractedData.workHistory || [],

        // Map experience level to root level
        experienceLevel: {
          totalYears: extractedData.experienceLevel?.totalYears || extractedData.totalExperience || 0,
          relevantYears: extractedData.experienceLevel?.relevantYears || extractedData.relevantExperience || 0
        },

        // Analysis results
        matchScore: analysis.score || analysis.matchScore || 0,
        matchedSkills: analysis.matchedSkills || [],
        missingSkills: analysis.missingSkills || [],
        matchQuality: analysis.matchQuality || "medium",
        finalVerdict: analysis.finalVerdict || "Analysis completed",
        scoreBreakdown: analysis.scoreBreakdown || {
          skillsMatchScore: 0,
          experienceScore: 0,
          educationScore: 0,
          jobTitleScore: 0,
          locationScore: 0
        },

        // Additional analysis data
        strengths: analysis.strengths || '',
        weaknesses: analysis.weaknesses || '',
        recommendations: analysis.recommendations || '',

        // Status tracking
        extractionStatus: 'completed',
        analysisStatus: 'completed',
        analyzed: true,
        lastUpdated: new Date().toISOString(),

        // Store candidate name
        name: extractedData.basicInfo?.fullName || extractedData.name || resumeName.split('.')[0]
      };

      // Update resume data
      resumes[resumeIndex] = {
        ...resumes[resumeIndex],
        ...resumeDataToSave
      };

      // Update the job document
      await updateDoc(jobRef, {
        resumes: resumes
      });

      console.log(`Successfully updated combined data for resume: ${resumeName}`);
      return true;

    } catch (retryError) {
      console.error(`Error in retry attempt ${retryCount + 1}:`, retryError);
      if (retryCount < MAX_RETRIES - 1) {
        await new Promise(resolve => setTimeout(resolve, RETRY_DELAY_MS));
      }
    }
  }

  console.error(`Failed to update resume data after ${MAX_RETRIES} retries`);
  return false;
}

// Main function to process multiple resumes in batch with combined extraction + analysis
async function processBatchComplete(
  resumes: Array<{resumeUrl: string, resumeName: string, resumeId: string}>,
  jobId: string,
  jobDetails: {title: string, location: string, description: string, skills: string}
) {
  console.log(`Starting combined batch processing for ${resumes.length} resumes`);

  try {
    // Separate supported and unsupported files
    const supportedResumes = resumes.filter(resume => isFileTypeSupportedByGemini(resume.resumeName));
    const unsupportedResumes = resumes.filter(resume => !isFileTypeSupportedByGemini(resume.resumeName));

    console.log(`Combined batch: ${supportedResumes.length} supported files, ${unsupportedResumes.length} unsupported files`);

    let allResults: any[] = [];

    // Process supported files in batch if any exist
    if (supportedResumes.length > 0) {
      console.log(`Processing ${supportedResumes.length} supported files with combined extraction + analysis`);

      // Download all supported files
      const downloadedFiles = await Promise.all(
        supportedResumes.map(async (resume) => {
          console.log(`Downloading file: ${resume.resumeName}`);
          const response = await fetch(resume.resumeUrl);

          if (!response.ok) {
            throw new Error(`Failed to download ${resume.resumeName}: ${response.status} ${response.statusText}`);
          }

          let arrayBuffer = await response.arrayBuffer();
          let fileBuffer = new Uint8Array(arrayBuffer);



          // Handle DOCX text extraction before sending to Gemini
          let base64Data: string;
          let mimeType: string;
          const originalMimeType = getContentType(resume.resumeName);
          const isDocxFile = originalMimeType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';

          if (isDocxFile) {
            console.log(`DOCX file detected in batch: ${resume.resumeName}, extracting text content...`);
            try {
              const result = await mammoth.extractRawText({buffer: Buffer.from(arrayBuffer)});
              const textContent = result.value;

              if (!textContent || textContent.trim().length === 0) {
                throw new Error(`No text content extracted from DOCX file: ${resume.resumeName}`);
              }

              base64Data = Buffer.from(textContent, 'utf-8').toString('base64');
              mimeType = 'text/plain';
              console.log(`DOCX text extraction successful for ${resume.resumeName}. Text length: ${textContent.length} characters`);
            } catch (extractionError) {
              console.error(`DOCX text extraction failed for ${resume.resumeName}:`, extractionError);
              throw new Error(`Failed to extract text from DOCX file ${resume.resumeName}: ${extractionError instanceof Error ? extractionError.message : 'Unknown error'}`);
            }
          } else {
            // Existing logic for other file types (PDF, TXT, etc.)
            base64Data = Buffer.from(arrayBuffer).toString('base64');
            mimeType = originalMimeType;
          }

          return {
            base64Data,
            mimeType: mimeType,
            fileName: resume.resumeName,
            resumeId: resume.resumeId,
            resumeUrl: resume.resumeUrl
          };
        })
      );

      console.log(`Successfully downloaded ${downloadedFiles.length} files for combined processing`);

      // Create the combined prompt for Gemini (extraction + analysis)
      const combinedPrompt = `
      Extract structured data from these ${downloadedFiles.length} resume files AND analyze them against the following job description in a single step.

      Job Title: ${jobDetails.title || 'Not specified'}
      Job Location: ${jobDetails.location || 'Not specified'}
      Required Skills: ${jobDetails.skills || 'Not specified'}
      Experience Requirements: ${(jobDetails as any).minExperience !== undefined && (jobDetails as any).maxExperience !== undefined
        ? `${(jobDetails as any).minExperience}-${(jobDetails as any).maxExperience} years`
        : (jobDetails as any).minExperience !== undefined
          ? `${(jobDetails as any).minExperience}+ years minimum`
          : (jobDetails as any).maxExperience !== undefined
            ? `Up to ${(jobDetails as any).maxExperience} years`
            : 'Not specified'}
      Job Description: ${jobDetails.description || 'Not specified'}

      For each resume file, perform BOTH extraction and analysis:

      EXTRACTION: Extract all relevant information including:
      1. Full name of the candidate
      2. Contact information (email, phone, location)
      3. Skills (as an array)
      4. Education history (degree, institution, graduation year)
      5. Work experience (job titles, companies, durations, responsibilities)
      6. Total years of experience and relevant years
      7. Certifications or special qualifications

      ANALYSIS: Analyze the extracted data against the job requirements:
      1. Calculate match score (0-100) based on skills, experience, education, job title relevance - consider both total years and relevant years against the specified experience requirements
      2. Identify matched skills (skills present in both resume and job requirements)
      3. Identify missing skills (skills in job requirements but not in resume)
      4. Determine match quality: "top" (80-100), "medium" (60-79), "low" (0-59)
      5. Provide final verdict in 2 lines
      6. Calculate detailed score breakdown

      Return ONLY a valid JSON array with ${downloadedFiles.length} objects, one for each resume file in the order they were provided. Each object should combine both extraction and analysis:

      {
        "extractedData": {
          "basicInfo": {
            "fullName": "Candidate's full name",
            "email": "Email address",
            "phone": "Phone number",
            "location": "Location"
          },
          "skills": ["Skill 1", "Skill 2", ...],
          "education": [
            {
              "degree": "Degree name",
              "institution": "Institution name",
              "graduationYear": "Year of graduation"
            }
          ],
          "experienceLevel": {
            "totalYears": Number of total years of experience,
            "relevantYears": Number of years of relevant experience
          },
          "workHistory": [
            {
              "position": "Job title",
              "company": "Company name",
              "duration": "Duration at company",
              "description": "Brief description of responsibilities"
            }
          ],
          "certifications": ["Certification 1", "Certification 2", ...]
        },
        "analysis": {
          "score": Match score (0-100),
          "matchQuality": "top" | "medium" | "low",
          "matchedSkills": ["Skill 1", "Skill 2", ...],
          "missingSkills": ["Missing Skill 1", "Missing Skill 2", ...],
          "scoreBreakdown": {
            "skillsMatchScore": Score for skills match (0-100),
            "experienceScore": Score for experience relevance (0-100),
            "educationScore": Score for education relevance (0-100),
            "jobTitleScore": Score for job title relevance (0-100),
            "locationScore": Score for location match (0-100)
          },
          "finalVerdict": "2-line summary of the candidate's fit for the role",
          "strengths": "Candidate's strengths relative to the job",
          "weaknesses": "Areas where the candidate could improve",
          "recommendations": "Recommendations for the hiring manager"
        }
      }

      Make sure to extract as much information as possible from each resume and provide thorough analysis. If any field is not available, use null or an empty array as appropriate.
      The response must be a valid JSON array with exactly ${downloadedFiles.length} objects.
      `;

      // Call Gemini API with all files for combined processing
      console.log('Calling Gemini API for combined batch processing (extraction + analysis)');

      const responseText = await callGeminiAPIWithMultipleFiles(
        combinedPrompt,
        downloadedFiles,
        "You are an expert resume processor. Extract structured data and analyze multiple resumes against job requirements in one step. Return a JSON array with combined extraction and analysis results."
      );

      console.log('Gemini API response received for combined processing');
      console.log('Response text from Gemini (first 200 chars):', responseText.substring(0, 200));

      // Parse the response
      let combinedResultsArray: any[];
      try {
        combinedResultsArray = JSON.parse(responseText);
        console.log('Parsed combined results from JSON response');

        if (!Array.isArray(combinedResultsArray)) {
          throw new Error('Response is not an array');
        }
      } catch (initialParseError) {
        console.log('Could not parse entire response as JSON array, trying to extract JSON array');

        // Try to extract JSON array using regex pattern
        const jsonMatch = responseText.match(/\[[\s\S]*\]/);
        if (jsonMatch) {
          console.log('JSON array match found in response');
          try {
            combinedResultsArray = JSON.parse(jsonMatch[0]);
            console.log('Parsed combined data from JSON array');

            if (!Array.isArray(combinedResultsArray)) {
              throw new Error('Extracted JSON is not an array');
            }
          } catch (jsonParseError) {
            console.error('Error parsing extracted JSON array:', jsonParseError);
            throw new Error('Failed to parse extracted JSON array from Gemini response');
          }
        } else {
          throw new Error('Failed to extract JSON array from Gemini response');
        }
      }

      // Process and save the combined results
      for (let i = 0; i < downloadedFiles.length && i < combinedResultsArray.length; i++) {
        const file = downloadedFiles[i];
        const combinedData = combinedResultsArray[i];

        if (combinedData) {
          const updateSuccess = await updateResumeWithCombinedData(jobId, file.resumeId, combinedData, file.fileName, file.resumeUrl);
          allResults.push({
            resumeId: file.resumeId,
            resumeName: file.fileName,
            success: updateSuccess,
            data: combinedData
          });

          if (updateSuccess) {
            console.log(`Successfully updated combined data for ${file.fileName}`);
          } else {
            console.error(`Failed to update combined data for ${file.fileName}`);
          }
        } else {
          console.error(`No combined data for resume ${file.fileName}`);
          allResults.push({
            resumeId: file.resumeId,
            resumeName: file.fileName,
            success: false,
            error: 'No combined data received'
          });
        }
      }
    }

    return {
      success: true,
      results: allResults,
      totalProcessed: allResults.length,
      successCount: allResults.filter(result => result.success).length,
      failureCount: allResults.filter(result => !result.success).length,
      unsupportedFiles: unsupportedResumes.map(resume => ({
        resumeId: resume.resumeId,
        resumeName: resume.resumeName,
        reason: 'File type not supported for batch processing - will be processed individually'
      })),
      unsupportedCount: unsupportedResumes.length
    };

  } catch (error) {
    console.error('Error in combined batch processing:', error);
    throw error;
  }
}

export async function POST(request: NextRequest) {
  try {
    console.log('API route called: /api/process-batch-complete');

    // Parse the request body
    const body = await request.json();
    const { resumes, jobId, jobDetails } = body;

    // Validate required fields
    if (!resumes || !Array.isArray(resumes) || resumes.length === 0) {
      console.error('Missing or invalid resumes array');
      return NextResponse.json(
        { error: 'Missing or invalid resumes array' },
        { status: 400 }
      );
    }

    if (!jobId) {
      console.error('Missing jobId');
      return NextResponse.json(
        { error: 'Missing jobId' },
        { status: 400 }
      );
    }

    if (!jobDetails) {
      console.error('Missing jobDetails');
      return NextResponse.json(
        { error: 'Missing jobDetails' },
        { status: 400 }
      );
    }

    // Validate batch size (max 5 resumes)
    if (resumes.length > 5) {
      console.error(`Batch size too large: ${resumes.length}. Maximum is 5.`);
      return NextResponse.json(
        { error: 'Batch size too large. Maximum is 5 resumes per batch.' },
        { status: 400 }
      );
    }

    // Validate each resume object
    for (const resume of resumes) {
      if (!resume.resumeUrl || !resume.resumeName || !resume.resumeId) {
        console.error('Invalid resume object:', resume);
        return NextResponse.json(
          { error: 'Each resume must have resumeUrl, resumeName, and resumeId' },
          { status: 400 }
        );
      }
    }

    // Try to verify the authentication token, but continue even if it fails
    let verifiedUserId;
    try {
      const authResult = await verifyAuthToken(request);

      if (authResult.error) {
        console.warn('Auth verification failed, using fallback user ID');
        verifiedUserId = body.userId || 'anonymous-user';
      } else {
        verifiedUserId = authResult.userId;
        console.log('Authenticated request from user:', verifiedUserId);
      }
    } catch (authError) {
      console.error('Error during auth verification:', authError);
      verifiedUserId = body.userId || 'anonymous-user';
      console.log('Using fallback user ID due to auth error:', verifiedUserId);
    }

    // Check if Gemini API key is configured
    if (!GEMINI_API_KEY) {
      console.error('Gemini API key is not configured');
      return NextResponse.json(
        { error: 'Gemini API key is not configured' },
        { status: 500 }
      );
    }

    try {
      // Process the batch with combined extraction + analysis
      const batchResults = await processBatchComplete(resumes, jobId, jobDetails);

      // Return the batch results
      return NextResponse.json({
        success: true,
        message: `Combined batch processing completed. ${batchResults.successCount} successful, ${batchResults.failureCount} failed.`,
        results: batchResults.results,
        totalProcessed: batchResults.totalProcessed,
        successCount: batchResults.successCount,
        failureCount: batchResults.failureCount,
        unsupportedFiles: batchResults.unsupportedFiles,
        unsupportedCount: batchResults.unsupportedCount
      });
    } catch (processingError) {
      console.error('Error during combined batch processing:', processingError);

      // Return a failure response
      return NextResponse.json({
        success: false,
        message: 'Combined batch processing failed',
        error: processingError instanceof Error ? processingError.message : 'Unknown processing error',
        results: []
      }, { status: 500 });
    }
  } catch (error) {
    console.error('Error in process-batch-complete API route:', error);

    return NextResponse.json(
      {
        error: 'Failed to process batch with combined method',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
